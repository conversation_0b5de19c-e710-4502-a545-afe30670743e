import { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ArrowLeft, Shield, Clock, CheckCircle, XCircle, Filter } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { Database } from '@/types/database';
import PaymentModerationCard from '@/components/PaymentModerationCard';

type Payment = Database['public']['Tables']['payments']['Row'] & {
  tanda_members: {
    payout_position: number;
    profiles: {
      full_name: string;
      avatar_url: string | null;
      trust_score: number;
    };
  };
};

type Tanda = Database['public']['Tables']['tandas']['Row'];

export default function ModerationScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { user } = useAuth();
  const [tanda, setTanda] = useState<Tanda | null>(null);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');

  useEffect(() => {
    if (id) {
      loadModerationData();
    }
  }, [id]);

  const loadModerationData = async () => {
    try {
      // Load tanda details
      const { data: tandaData, error: tandaError } = await supabase
        .from('tandas')
        .select('*')
        .eq('id', id)
        .single();

      if (tandaError) throw tandaError;
      setTanda(tandaData);

      // Load payments with member details
      const { data: paymentsData, error: paymentsError } = await supabase
        .from('payments')
        .select(`
          *,
          tanda_members (
            payout_position,
            profiles (
              full_name,
              avatar_url,
              trust_score
            )
          )
        `)
        .eq('tanda_id', id)
        .order('cycle_number', { ascending: true })
        .order('created_at', { ascending: true });

      if (paymentsError) throw paymentsError;
      setPayments(paymentsData as Payment[]);
    } catch (error) {
      console.error('Error loading moderation data:', error);
      Alert.alert('Error', 'Failed to load payment data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadModerationData();
  };

  const handlePaymentApproved = () => {
    loadModerationData(); // Refresh data
  };

  const handlePaymentRejected = () => {
    loadModerationData(); // Refresh data
  };

  const filteredPayments = payments.filter(payment => {
    if (filterStatus === 'all') return true;
    return payment.status === filterStatus;
  });

  const getStatusCounts = () => {
    return {
      pending: payments.filter(p => p.status === 'pending').length,
      approved: payments.filter(p => p.status === 'approved').length,
      rejected: payments.filter(p => p.status === 'rejected').length,
    };
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} color={colors.warning} strokeWidth={2} />;
      case 'approved':
        return <CheckCircle size={16} color={colors.success} strokeWidth={2} />;
      case 'rejected':
        return <XCircle size={16} color={colors.error} strokeWidth={2} />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return colors.warning;
      case 'approved':
        return colors.success;
      case 'rejected':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.loadingText}>Loading...</Text>
      </SafeAreaView>
    );
  }

  if (!tanda) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.errorText}>Tanda not found</Text>
      </SafeAreaView>
    );
  }

  // Check if user is the organizer
  if (tanda.created_by !== user?.id) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.errorText}>You are not authorized to moderate this tanda</Text>
      </SafeAreaView>
    );
  }

  const statusCounts = getStatusCounts();

  return (
    <View style={styles.container}>
      <SafeAreaView edges={['top']} style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} strokeWidth={2} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <Text style={styles.headerTitle}>Payment Moderation</Text>
            <Text style={styles.headerSubtitle}>{tanda.name}</Text>
          </View>
        </View>
      </SafeAreaView>

      <View style={styles.statsSection}>
        <View style={styles.statsRow}>
          <View style={styles.statCard}>
            <Clock size={20} color={colors.warning} strokeWidth={2} />
            <Text style={styles.statNumber}>{statusCounts.pending}</Text>
            <Text style={styles.statLabel}>Pending</Text>
          </View>
          <View style={styles.statCard}>
            <CheckCircle size={20} color={colors.success} strokeWidth={2} />
            <Text style={styles.statNumber}>{statusCounts.approved}</Text>
            <Text style={styles.statLabel}>Approved</Text>
          </View>
          <View style={styles.statCard}>
            <XCircle size={20} color={colors.error} strokeWidth={2} />
            <Text style={styles.statNumber}>{statusCounts.rejected}</Text>
            <Text style={styles.statLabel}>Rejected</Text>
          </View>
        </View>
      </View>

      <View style={styles.filterSection}>
        <Text style={styles.filterTitle}>Filter by Status</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          {(['all', 'pending', 'approved', 'rejected'] as const).map((status) => (
            <TouchableOpacity
              key={status}
              style={[
                styles.filterButton,
                filterStatus === status && styles.filterButtonActive
              ]}
              onPress={() => setFilterStatus(status)}>
              {status !== 'all' && getStatusIcon(status)}
              <Text style={[
                styles.filterButtonText,
                filterStatus === status && styles.filterButtonTextActive
              ]}>
                {status === 'all' ? 'All' : status.charAt(0).toUpperCase() + status.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        
        {filteredPayments.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Shield size={48} color={colors.gray400} strokeWidth={1.5} />
            <Text style={styles.emptyTitle}>No Payments Found</Text>
            <Text style={styles.emptyDescription}>
              {filterStatus === 'all' 
                ? 'No payment proofs have been uploaded yet.'
                : `No payments with status "${filterStatus}" found.`}
            </Text>
          </View>
        ) : (
          <View style={styles.paymentsList}>
            {filteredPayments.map((payment) => (
              <PaymentModerationCard
                key={payment.id}
                payment={payment}
                onApprove={handlePaymentApproved}
                onReject={handlePaymentRejected}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    ...typography.h4,
    color: colors.text,
    fontWeight: '700',
  },
  headerSubtitle: {
    ...typography.body,
    color: colors.textSecondary,
  },
  statsSection: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
  },
  statCard: {
    alignItems: 'center',
    gap: 4,
  },
  statNumber: {
    ...typography.h3,
    color: colors.text,
    fontWeight: '700',
  },
  statLabel: {
    ...typography.small,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  filterSection: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  filterTitle: {
    ...typography.bodyMedium,
    color: colors.text,
    fontWeight: '600',
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  filterScroll: {
    paddingHorizontal: 16,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.gray100,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
  },
  filterButtonText: {
    ...typography.bodyMedium,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  filterButtonTextActive: {
    color: colors.white,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  paymentsList: {
    padding: 16,
    gap: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyTitle: {
    ...typography.h4,
    color: colors.text,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  loadingText: {
    ...typography.h4,
    color: colors.text,
    textAlign: 'center',
    marginTop: 64,
  },
  errorText: {
    ...typography.h4,
    color: colors.error,
    textAlign: 'center',
    marginTop: 64,
  },
});
